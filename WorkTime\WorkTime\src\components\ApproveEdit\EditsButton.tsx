import React from "react";
import styled from "styled-components";

interface EditsButtonProps {
  variant: "cancel" | "confirm";
  onClick?: () => void;
  text: string | React.ReactNode;
  icon: string;
  dataTestId?: string;
}

const StyledButton = styled.button<{ variant: "cancel" | "confirm" }>`
  background: none;
  border: 2px solid var(--profile-tab-label-border-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  padding: 0.5rem 2rem 0.5rem 1rem;
  border-radius: 4rem;
  transition: all 0.2s ease;

  &:hover,
  &:focus {
    border-color: ${(props) =>
      props.variant === "cancel"
        ? "var(--error-alert-color)"
        : "var(--success-alert-color)"};
  }
`;

const Icon = styled.img`
  width: 0.8rem;
  height: 0.8rem;
`;

const EditsButton: React.FC<EditsButtonProps> = ({
  variant,
  onClick,
  text,
  icon,
  dataTestId,
}) => {
  return (
    <StyledButton variant={variant} onClick={onClick} data-testid={dataTestId}>
      <Icon src={icon} />
      {text}
    </StyledButton>
  );
};

export default EditsButton;
